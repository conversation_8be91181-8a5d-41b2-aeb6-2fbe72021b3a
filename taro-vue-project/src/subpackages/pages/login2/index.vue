<template>
  <!-- 登录页面 - 从WePY2迁移到Taro + Vue3 -->
  <view class="container">
    <!-- 背景图片 -->
    <image
      v-if="type === 0"
      src="https://cos.annmun1.net/gIJwB2U9JSw9c381ecc5698325b8de35f0d2d7569b83.png"
      class="bg"
    />
    <image
      v-if="type === 1"
      src="https://cos.annmun1.net/omNNGLHyTNey12434fec966470964b0ae5426cb50975.png"
      class="bg"
    />

    <!-- 标签切换 -->
    <view class="tabs">
      <view class="col1" :class="{ active: type === 0 }" @tap="onChangeTab(0)">
        <view class="zh">已有帐号</view>
        <view class="en">I'VE BEEN HERE</view>
      </view>
      <view class="col2" :class="{ active: type === 1 }" @tap="onChangeTab(1)">
        <view class="zh">我是新人</view>
        <view class="en">I'M NEW</view>
      </view>
    </view>

    <!-- 登录表单 -->
    <view v-if="type === 0" class="member">
      <!-- 用户名密码登录 -->
      <view v-if="loginType === 'u'" class="box box1">
        <view class="row row1">
          <view class="key">用户名</view>
          <view class="val">
            <nut-input
              v-model="account"
              placeholder="请输入用户名"
              :border="false"
            />
          </view>
        </view>
        <view class="row row2">
          <view class="key">密码</view>
          <view class="val">
            <nut-input
              v-model="psw"
              type="password"
              placeholder="请输入密码"
              :border="false"
            />
          </view>
        </view>
      </view>

      <!-- 手机号验证码登录 -->
      <view v-if="loginType === 'p'" class="box box1">
        <view class="row row1">
          <view class="key">手机号</view>
          <view class="val">
            <nut-input
              v-model="phone"
              type="number"
              placeholder="请输入手机号"
              maxlength="11"
              :border="false"
            />
          </view>
        </view>
        <view class="row row2">
          <view class="key">验证码</view>
          <view class="val">
            <nut-input
              v-model="code"
              type="number"
              placeholder="请输入验证码"
              :border="false"
            />
            <view
              class="code"
              :class="{ disabled: seconds !== 60 }"
              @tap="getSms"
            >
              {{ seconds === 60 ? "获取验证码" : seconds + "s后重新获取" }}
            </view>
          </view>
        </view>
      </view>

      <!-- 登录方式切换 -->
      <view class="loginType" @tap="onChangeLoginType">
        {{ loginType === "u" ? "手机号登录" : "帐号密码登录" }}
      </view>

      <!-- 其他登录选项 -->
      <view class="other">
        <!-- 手机号快捷登录 - 仅在微信小程序环境下显示 -->
        <button
          v-if="isWeapp"
          class="col1"
          open-type="getPhoneNumber"
          @getphonenumber="getPhoneNumber"
        >
          手机号快捷登录
        </button>
        <view class="col2" @tap="goToResetPassword">
          忘记密码? <text>找回密码</text>
        </view>
      </view>

      <!-- 登录按钮 -->
      <view class="btns">
        <button class="btn3-bg" @tap="onAccountLogin">
          <text>登 录</text>
        </button>
      </view>
    </view>

    <!-- 注册表单 -->
    <view v-else class="new">
      <view class="box box2">
        <view class="row row1">
          <view class="key">手机号</view>
          <view class="val">
            <nut-input
              v-model="phone"
              type="number"
              placeholder="请输入手机号"
              :border="false"
            />
          </view>
        </view>
        <view class="row row2">
          <view class="key">验证码</view>
          <view class="val">
            <nut-input
              v-model="code"
              type="number"
              placeholder="请输入验证码"
              :border="false"
            />
            <view
              class="code"
              :class="{ disabled: seconds !== 60 }"
              @tap="getSms"
            >
              {{ seconds === 60 ? "获取验证码" : seconds + "s后重新获取" }}
            </view>
          </view>
        </view>
        <view class="row row3">
          <view class="key">分享人</view>
          <view class="val">
            <nut-input
              v-model="shareCode"
              placeholder="请输入分享码或分享手机号"
              :border="false"
            />
          </view>
        </view>
        <view class="row row3">
          <view class="key">注册地址</view>
          <view class="val">
            <picker
              mode="multiSelector"
              :value="currRegionIndex"
              range-key="name"
              :range="regions"
              @change="bindRegionIndexPickerChange"
              @columnchange="bindcolumnchange"
            >
              <view v-if="!regionStr"> 请选择注册地 </view>
              <view v-else style="font-size: 30rpx; color: #131313">
                {{ regionStr }}
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 快捷注册链接 -->
      <view
        v-if="isWeapp && !newCompany"
        class="wx-register"
        @tap="goToShareRegister"
      >
        切换手机号快捷注册
      </view>

      <!-- 注册按钮 -->
      <view class="btns">
        <button class="btn3-bg" @tap="onRegister">
          <text>注 册</text>
        </button>
      </view>
    </view>

    <!-- 充值弹窗 -->
    <nut-popup
      v-model="showPay"
      position="bottom"
      :close-on-click-overlay="false"
      round
    >
      <view class="recharge">
        <view class="header">
          <view class="title">代理商充值金额</view>
          <text class="close" @tap="onCloseServicesPay">×</text>
        </view>
        <nut-input
          v-model="rechargeAmount"
          type="digit"
          disabled
          placeholder="请输入金额"
          @input="keyInput"
        />
        <view class="protocol">
          <view class="left">
            <nut-checkbox v-model="protocolChecked" icon-size="18">
              <text style="color: #999">同意</text>
            </nut-checkbox>
          </view>
          <view class="right">《盛天喜美充值协议》</view>
        </view>
        <view class="btn" @tap="onRecharge">充值并支付</view>
      </view>
    </nut-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import Taro, { useLoad, useDidShow } from "@tarojs/taro";
// 引入API服务 - 从WePY项目迁移
import {
  fetchLogin,
  getUserInfo,
  GetPhoneNo,
  getCode,
  WechatRegisterMember,
  SFWalletTopUpSC,
  getState,
  getCity,
  getDistrict,
} from "../../../services/api";
import { getCurrentPageUrlWithArgs } from "../../../services/request";
import { limitCash, getLocation } from "../../../utils";
// import homeIconImg from '../../../static/tabBar/tab1-selected.png'
import "./index.less";

// 定义接口类型
interface LocationInfo {
  State: string;
  City: string;
  District: string;
}

interface RegionItem {
  name: string;
  AreaCode?: string;
  AreaDescrip?: string;
  CityCode?: string;
  CityDescrip?: string;
  DistrictCode?: string;
  DistrictDescrip?: string;
}

// 响应式数据
const statusBarHeight = ref(0); // 状态栏高度
const titleHeight = ref(0); // 标题高度
const pageLen = ref(1); // 路由栈数量
const type = ref(0); // 0:登录 1:注册
const loginType = ref("p"); // u:帐号 p:手机
const account = ref("");
const psw = ref("");
const phone = ref("");
const code = ref("");
const shareCode = ref("");
const seconds = ref(60);
const completeRegister = ref(false); // 是否完成注册
const showPay = ref(false); // 代理商充值
const protocolChecked = ref(false); // 充值协议
const rechargeAmount = ref(""); // 充值余额
const payLoading = ref(false);
const regionStr = ref(""); // 注册地
const regions = ref<RegionItem[][]>([[], [], []]);
const regionsCode = ref<string[]>([]);
const currRegionIndex = ref([0, 0, 0]);
const newCompany = ref(false); // 是否是超出3万需注册新账号的情况
const location = ref<LocationInfo | null>(null);
const CountryCode = ref("CN");

// 静态资源
const homeIcon = "../../../static/tabBar/tab1-selected.png";

// 环境检测
const isWeapp = ref(false); // 是否为微信小程序环境

// 计算属性
const s = 60;

// 方法定义
const showToast = (message: string) => {
  Taro.showToast({
    title: message,
    icon: "none",
    duration: 2000,
  });
};

const goHome = () => {
  Taro.switchTab({ url: "/pages/index/index" });
};

const goBack = () => {
  Taro.navigateBack();
};

const onChangeTab = (tabType: number) => {
  if (tabType === 0) {
    account.value = "";
    psw.value = "";
    loginType.value = "p";
  } else {
    phone.value = "";
    code.value = "";
  }
  type.value = tabType;
};

const onChangeLoginType = () => {
  if (loginType.value === "u") {
    phone.value = "";
    code.value = "";
    loginType.value = "p";
  } else {
    account.value = "";
    psw.value = "";
    loginType.value = "u";
  }
};

const onCloseServicesPay = () => {
  showPay.value = false;
  servicesPay();
};

const servicesPay = () => {
  const userInfo = Taro.getStorageSync("userInfo");
  if (userInfo?.TotalTopUp > 0 && userInfo?.SCTopUp === "False") {
    return Taro.showModal({
      title: "温馨提示",
      content: "您需完成充值支付，才可继续使用",
      showCancel: false,
      confirmText: "充值",
      success: (res) => {
        if (res.confirm) {
          rechargeAmount.value = userInfo?.TotalTopUp;
          showPay.value = true;
        }
      },
    });
  } else {
    return false;
  }
};

const onRedirect = () => {
  const currentPage = Taro.getStorageSync("currentPage");
  const tabUrl = [
    "pages/index/index",
    "pages/classification/index",
    "pages/cart/index",
    "pages/my/index",
  ];
  if (currentPage) {
    if (tabUrl.includes(currentPage)) {
      Taro.switchTab({ url: "/" + currentPage });
    } else {
      Taro.redirectTo({ url: "/" + currentPage });
    }
    Taro.removeStorageSync("currentPage");
  } else {
    Taro.switchTab({ url: "/pages/index/index" });
  }
};

const onWxLogin = (data: any) => {
  Taro.showLoading({ mask: true, title: "登录中..." });
  if (isWeapp.value) {
    Taro.login({
      success(res) {
        if (res.code) {
          data.Code = res.code;
          Taro.showLoading({ mask: true, title: "登录中..." });
          fetchLogin(data)
            .then((res: any) => {
              const data = res.data.d[0];
              Taro.removeStorageSync("IntroducerID");
              Taro.setStorageSync("Token", data.Token);
              Taro.setStorageSync("MemberCode", data.MemberCode);
              Taro.setStorageSync("groupId", data.GroupID);
              getUserInfo({
                Token: data.Token,
                MemberCode: data.MemberCode,
              }).then((res: any) => {
                if (completeRegister.value) {
                  Taro.setStorageSync("tipChangePsw", 1);
                }
                const userInfo = res.data.d[0];
                Taro.setStorageSync("userInfo", JSON.stringify(userInfo));
                onRedirect();
              });
            })
            .finally(() => {
              Taro.hideLoading();
            })
            .catch(() => {
              // 错误处理已在request中处理
            });
        } else {
          showToast("获取微信code失败，请重试");
          console.log("登录失败！" + res.errMsg);
        }
      },
      fail(err) {
        Taro.hideLoading();
        showToast(err.errMsg);
      },
    });
  } else {
    data.Code = "";
    console.log(3333, data);
    fetchLogin(data)
      .then((res: any) => {
        const data = res.data.d[0];
        Taro.removeStorageSync("IntroducerID");
        Taro.setStorageSync("Token", data.Token);
        Taro.setStorageSync("MemberCode", data.MemberCode);
        Taro.setStorageSync("groupId", data.GroupID);
        getUserInfo({
          Token: data.Token,
          MemberCode: data.MemberCode,
          CountryCode: CountryCode.value,
        }).then((res: any) => {
          if (completeRegister.value) {
            Taro.setStorageSync("tipChangePsw", 1);
          }
          const userInfo = res.data.d[0];
          Taro.setStorageSync("userInfo", JSON.stringify(userInfo));
          onRedirect();
        });
      })
      .finally(() => {
        Taro.hideLoading();
      })
      .catch(() => {
        // 错误处理已在request中处理
      });
  }
};

const onPhoneLogin = (p: any) => {
  const data = {
    LoginType: "P",
    UsernameOrPhone: p.PhoneNo,
    LoginValue: p.VCode,
  };
  onWxLogin(data);
};

const onAccountLogin = () => {
  if (!phone.value) return showToast("请输入手机号");
  if (!code.value) return showToast("请输入验证码");

  let data: any = {};
  if (loginType.value === "u") {
    if (!account.value) return showToast("请输入帐号");
    if (!psw.value) return showToast("请输入密码");
    data.UsernameOrPhone = account.value;
    data.LoginType = "U";
    data.LoginValue = psw.value;
  } else {
    if (!phone) return showToast("请输入手机号");
    if (!code) return showToast("请输入验证码");
    data.UsernameOrPhone = "86" + phone.value;
    data.LoginType = "P";
    data.LoginValue = code.value;
  }
  onWxLogin(data);
};

const getPhoneNumber = (e: any) => {
  const { code } = e.detail;
  if (!code) return;
  Taro.showLoading({ mask: true, title: "获取手机号..." });
  GetPhoneNo({ Code: code })
    .then((res: any) => {
      const data = res.data.d[0];
      if (+data.RegisterStatus === 1) {
        onPhoneLogin(data);
      } else {
        showToast("请还未注册，请进行注册。");
        type.value = 1;
      }
    })
    .finally(() => {
      Taro.hideLoading();
    });
};

const getSms = () => {
  if (seconds.value !== s) return false;
  if (!phone.value) {
    return showToast("请填写手机号码");
  }
  seconds.value--;
  const timer = setInterval(() => {
    if (seconds.value <= 0) {
      seconds.value = s;
      return clearInterval(timer);
    }
    seconds.value--;
  }, 1000);
  Taro.showLoading({ mask: true, title: "请求中" });
  getCode({
    PhoneNo: "86" + phone.value,
    Login: 0,
  }).finally(() => {
    showToast("验证码已发送");
    Taro.hideLoading();
  });
};

const goToResetPassword = () => {
  Taro.navigateTo({ url: "/subpackages/pages/resetPsw/index" });
};

const goToShareRegister = () => {
  Taro.redirectTo({ url: "/subpackages/pages/shareRegister/index" });
};

const onRegister = () => {
  if (!phone.value) return showToast("请填写手机号");
  if (!code.value) return showToast("请填写验证码");
  if (!shareCode.value) return showToast("请填写邀请码");

  if (!location.value) {
    showToast("正在获取当前定位信息，请再次进行注册");
    setTimeout(() => {
      handleReLocation();
    }, 2000);
  }
  let registerData = {
    IntroducerID: shareCode.value,
    PhoneNo: "86" + phone.value,
    VerificationCode: code.value,
    State: location.value.State,
    City: location.value.City,
    District: location.value.District,
    NewCompanyOldMemberCode: newCompany
      ? Taro.getStorageSync("MemberCode")
      : "",
  };
  Taro.showLoading({ mask: true, title: "注册中" });
  WechatRegisterMember(registerData)
    .then((_res) => {
      showToast("注册成功");
      completeRegister.value = true;
      Taro.setStorageSync("firstLogin", 1);
      Taro.removeStorage({ key: "IntroducerID" });
      onPhoneLogin({
        PhoneNo: registerData.PhoneNo,
        VCode: registerData.VerificationCode,
      });
    })
    .finally(() => {
      Taro.hideLoading();
    });
};

// 格式化输入充值金额
const keyInput = (value: string) => {
  rechargeAmount.value = limitCash(value) as string;
};

// 充值
const onRecharge = () => {
  if (payLoading.value) return;
  if (!protocolChecked.value) {
    return showToast("请阅读并同意《盛天喜美充值协议》");
  }
  if (+rechargeAmount.value <= 0) {
    return showToast("请输入充值金额");
  }

  payLoading.value = true;
  SFWalletTopUpSC({
    TopUpAmt: rechargeAmount.value,
  })
    .then((res: any) => {
      let data = res.data.d && res.data.d[0];
      if (data.Result && typeof data.Result === "string") {
        data.Result = JSON.parse(data.Result);
        rechargeAmount.value = "";

        if (data.MessageToUser === "PendingPayment(WC)") {
          Taro.requestPayment({
            ...data.Result,
            success: (_: any) => {
              getUserInfo().then((res: any) => {
                const data = res.data.d[0];
                Taro.setStorageSync("userInfo", JSON.stringify(data));
                onRedirect();
              });
            },
            fail: (_: any) => {
              console.log("支付失败");
              servicesPay();
            },
          });
        } else {
          const rc_Result = data.Result.rc_Result
            ? JSON.parse(data.Result.rc_Result)
            : data.Result.rc_Result;
          Taro.navigateTo({
            url: `/pages/pay?trxNo=${data.Result.r7_TrxNo}&price=${data.Result.r3_Amount}&appId=${rc_Result?.tradeAppid}&from=servicesPay`,
          });
        }
      }
    })
    .finally(() => {
      showPay.value = false;
      payLoading.value = false;
    });
};

// 获取位置
const handleReLocation = async () => {
  try {
    const locationInfo = await getLocation();
    if (locationInfo && locationInfo.District) {
      console.log("当前位置信息：", locationInfo);
      location.value = locationInfo;
      regionStr.value =
        locationInfo.State + locationInfo.City + locationInfo.District;
      Taro.setStorageSync("location", JSON.stringify(locationInfo));
    }
  } catch (error) {
    console.error("获取位置失败：", error);
  }
};

const bindRegionIndexPickerChange = (e: any) => {
  const res = e.detail.value;
  if (regions.value.some((item) => item.length === 0))
    return showToast("请选择完整地区");

  const newData = [...regionsCode.value];
  newData[0] = regions.value[0][res[0]].AreaCode || "";
  newData[1] = regions.value[1][res[1]].CityCode || "";
  newData[2] = regions.value[2][res[2]].DistrictCode || "";
  regionsCode.value = newData;
  currRegionIndex.value = res;

  location.value = {
    State: regions.value[0][res[0]].AreaDescrip || "",
    City: regions.value[1][res[1]].CityDescrip || "",
    District: regions.value[2][res[2]].DistrictDescrip || "",
  };

  regionStr.value =
    (regions.value[0][res[0]].AreaDescrip || "") +
    (regions.value[1][res[1]].CityDescrip || "") +
    (regions.value[2][res[2]].DistrictDescrip || "");
};

const bindcolumnchange = async (e: any) => {
  const detail = e.detail;
  const newData = [...currRegionIndex.value];
  newData[detail.column] = detail.value;
  currRegionIndex.value = newData;

  switch (detail.column) {
    case 0:
      newData[1] = 0;
      newData[2] = 0;
      await fetchCity();
      await fetchDistrict();
      break;
    case 1:
      newData[2] = 0;
      await fetchDistrict();
      break;
  }
};

const fetchState = () => {
  return new Promise<void>((resolve) => {
    const newData = [...regions.value];
    Taro.showLoading({ mask: true, title: "数据请求中" });
    getState({ CountryCode: CountryCode.value })
      .then((res: any) => {
        newData[0] = res.data.d
          .filter((item: any) => item.AreaCode)
          .map((item: any) => ({ ...item, name: item.AreaDescrip }));
        regions.value = newData;
        resolve();
      })
      .finally(() => {
        Taro.hideLoading();
      });
  });
};

const fetchCity = () => {
  return new Promise<void>((resolve) => {
    const newData = [...regions.value];
    if (
      newData[0] &&
      newData[0][currRegionIndex.value[0]] &&
      newData[0][currRegionIndex.value[0]].AreaCode
    ) {
      Taro.showLoading({ mask: true, title: "数据请求中" });
      getCity({ AreaCode: newData[0][currRegionIndex.value[0]].AreaCode })
        .then((res: any) => {
          newData[1] = res.data.d
            .filter((item: any) => item.CityCode)
            .map((item: any) => ({ ...item, name: item.CityDescrip }));
          regions.value = newData;
          resolve();
        })
        .finally(() => {
          Taro.hideLoading();
        });
    } else {
      resolve();
    }
  });
};

const fetchDistrict = () => {
  return new Promise<void>((resolve) => {
    const newData = [...regions.value];
    if (
      newData[1] &&
      newData[1][currRegionIndex.value[1]] &&
      newData[1][currRegionIndex.value[1]].CityCode
    ) {
      Taro.showLoading({ mask: true, title: "数据请求中" });
      getDistrict({ CityCode: newData[1][currRegionIndex.value[1]].CityCode })
        .then((res: any) => {
          newData[2] = res.data.d
            .filter((item: any) => item.DistrictCode)
            .map((item: any) => ({ ...item, name: item.DistrictDescrip }));
          regions.value = newData;
          resolve();
        })
        .finally(() => {
          Taro.hideLoading();
        });
    } else {
      resolve();
    }
  });
};

// 页面加载
useLoad(async (options) => {
  // 检测当前运行环境
  const env = Taro.getEnv();
  isWeapp.value = env === Taro.ENV_TYPE.WEAPP;
  console.log("当前环境:", env, "是否为微信小程序:", isWeapp.value);

  let userInfo = Taro.getStorageSync("userInfo");
  if (userInfo) userInfo = JSON.parse(userInfo);
  CountryCode.value = userInfo?.Country || "CN";

  const introducerID = Taro.getStorageSync("introducerID");
  let locationData = Taro.getStorageSync("location");
  if (locationData) {
    locationData = JSON.parse(locationData);
    location.value = locationData;
    regionStr.value =
      locationData.State + locationData.City + locationData.District;
  } else {
    handleReLocation();
  }

  const {
    type: optionType,
    shareCode: optionShareCode,
    newCompany: optionNewCompany,
  } = options;
  const systemInfo = Taro.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 0;
  titleHeight.value = 44; // 默认标题栏高度

  if (optionType) type.value = +optionType;
  if (optionNewCompany) {
    // 超出3万，无法成为2类公司，注册新公司
    newCompany.value = true;
    shareCode.value = Taro.getStorageSync("MemberCode");
  }
  if (optionShareCode || introducerID)
    shareCode.value = optionShareCode || introducerID;

  await fetchState();
  await fetchCity();
  await fetchDistrict();
  Taro.removeStorageSync("selectAddress");
});

// 页面显示
useDidShow(() => {
  pageLen.value = getCurrentPageUrlWithArgs().pages.length;
});
</script>
