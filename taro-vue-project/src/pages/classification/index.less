/* 分类页面样式 - 从WePY2迁移到Taro + Vue3 */
@import '../../common/common.less';

/* 页面基础样式 */
page {
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* 搜索头部样式 */
.header {
  position: relative;
  .flex();
  width: 100%;
  min-height: 88rpx;
  background-color: @primary-color;
  padding: 0 20rpx;
  box-sizing: border-box;
  
  input {
    flex: 1;
    height: 68rpx;
    background: #ffffff;
    border-radius: 34rpx;
    font-size: 28rpx;
    padding: 0 70rpx 0 20rpx;
  }
  
  .clear {
    position: absolute;
    right: 120rpx;
    color: #ccc;
    font-size: 35rpx;
  }
  
  .search {
    font-size: 30rpx;
    margin-left: 20rpx;
    color: #fff;
  }
}

/* Tab切换样式 */
.country {
  position: relative;
  min-height: 88rpx;
  border-bottom: 1rpx solid #eee;
}

.open-country {
  position: fixed;
  left: 0;
  top: 88rpx;
  width: 100%;
  height: auto;
  z-index: 99;
  box-shadow: 0px 14px 14px 0px rgba(0,0,0,0.28);
  
  .icon {
    transform: rotate(90deg);
  }
}

/* 主内容区域 */
.main {
  flex: 1;
  height: 1px;
  .flex();
  
  /* 左侧导航 */
  .nav {
    width: 206rpx;
    height: 100%;
    background-color: #f6f6f6;
    font-size: 26rpx;
    color: #0d0d0d;
    
    &-item {
      padding-top: 30rpx;
      box-sizing: border-box;
      
      &:last-child {
        padding-bottom: 30rpx;
      }
      
      > .name {
        .flex(center, center);
        width: 166rpx;
        padding: 0 19rpx;
        box-sizing: border-box;
        background: #ffffff;
        border-radius: 10rpx;
        margin: auto;
        font-size: 26rpx;
        border: 5rpx solid #fff;
        color: #525252;
        box-shadow: 0px 4px 11px -6px rgba(0, 0, 0, 0.46);
      }
      
      .activity-con {
        .activity-item {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 20rpx;
          
          image {
            width: 48rpx;
            height: 48rpx;
          }
          
          .name {
            font-size: 28rpx;
            color: #202020;
            font-weight: bold;
            font-style: italic;
          }
          
          .active {
            color: #99331C;
          }
        }
      }
      
      .normal {
        height: 65rpx;
        line-height: 65rpx;
        .ellipsis();
      }
      
      > .active {
        height: auto;
        min-height: 65rpx;
        border-color: #156F87;
      }
      
      .category-type {
        display: inline-block;
        font-size: 28rpx;
        font-weight: bold;
        width: 166rpx;
        padding: 30rpx 0 30rpx 40rpx;
        text-align: left;
      }
      
      .child-list {
        text-align: center;
        
        .child {
          .flex(center, start);
          width: 166rpx;
          padding: 15rpx 0 15rpx 20rpx;
          box-sizing: border-box;
          margin: auto;
          
          .text {
            display: inline-block;
            flex: 1;
            text-align: left;
            word-break: break-all;
          }
        }
        
        .normal {
          .text {
            max-width: 120rpx;
            .ellipsis();
          }
        }
        
        .active {
          color: #156F87;
          
          .icon {
            transform: rotate(90deg);
          }
        }
      }
    }
  }
  
  /* 右侧商品列表 */
  .pro {
    flex: 1;
    height: 100%;
  }
}

/* 骨架屏样式 */
.skeleton {
  .skeleton-header {
    height: 88rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  .skeleton-tabs {
    height: 88rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  .skeleton-main {
    display: flex;
    flex: 1;
    
    .skeleton-nav {
      width: 206rpx;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    
    .skeleton-content {
      flex: 1;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
