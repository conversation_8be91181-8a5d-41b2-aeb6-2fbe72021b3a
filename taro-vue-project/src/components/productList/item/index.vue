<template>
  <view class="product-item" @tap="onTap">
    <view class="product-image">
      <image :src="data.Image || '/static/default.jpg'" mode="aspectFill" class="image" />
    </view>
    <view class="product-info">
      <view class="product-name">{{ data.ProductDescrip || '商品名称' }}</view>
      <view class="price-info">
        <view class="current-price">¥{{ data.SalesPrice || '0.00' }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue'

interface ProductData {
  ProductCode?: string
  ProductDescrip?: string
  Image?: string
  SalesPrice?: string | number
  [key: string]: any
}

interface Props {
  data: ProductData
  size?: 'normal' | 'small'
  customStyle?: string
  hover?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
  size: 'normal',
  customStyle: '',
  hover: true
})

const emit = defineEmits<{
  tap: [data: ProductData]
}>()

const onTap = () => {
  emit('tap', props.data)
}
</script>

<style lang="less" scoped>
.product-item {
  display: flex;
  background: #fff;
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
  margin-bottom: 20rpx;
}

.product-image {
  position: relative;
  width: 240rpx;
  height: 240rpx;
  flex-shrink: 0;
}

.image {
  width: 100%;
  height: 100%;
}

.product-info {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 12rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.current-price {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
  margin-right: 16rpx;
}
</style>
